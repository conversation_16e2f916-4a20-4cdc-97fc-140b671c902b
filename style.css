
/* === FONT IMPORT === */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,300;0,400;0,500;0,600;0,700&family=Neonderthaw&display=swap');

/* ==========================================================================
   #DESIGN SYSTEM VARIABLES (THEME CORE)
   ========================================================================== */
:root {
  /* === COLOR PALETTE === */
  --primary: #7C2D92;
  --secondary: #9333EA;
  --accent: #A855F7;
  --dark-base: #0D0615;
  --dark-secondary: #1A0B2E;
  --dark-tertiary: #2D1B4E;

  /* === GRADIENTS === */
  --gradient-main-bg: linear-gradient(135deg, #0D0615 0%, #1A0B2E 50%, #2D1B4E 100%);
  --gradient-card-hover: linear-gradient(135deg, rgba(124, 45, 146, 0.20) 0%, rgba(147, 51, 234, 0.12) 100%);
  --gradient-accent-btn: linear-gradient(135deg, var(--primary), var(--secondary));
  --gradient-text: linear-gradient(135deg, var(--secondary), var(--accent));
  
  /* === GLASSMORPHISM === */
  --glass-bg: rgba(147, 51, 234, 0.08);
  --glass-border: rgba(168, 85, 247, 0.2);
  --glass-border-strong: rgba(168, 85, 247, 0.35);
  --glass-shadow: 0 8px 32px rgba(13, 6, 21, 0.8);
  --glass-blur: blur(16px); /* Adjusted for better performance */

  /* === TEXT & OUTLINES === */
  --text-primary: #FFFFFF;        /* Use for main headlines */
  --text-secondary: #E8E0F5;    /* Use for body copy, good readability */
  --text-tertiary: #D1C7E0;     /* Use for sub-text, check contrast */
  --text-muted: #B794C7;         /* Use for disabled/minor info, check contrast */
  --outline-glow: 0 0 25px rgba(168, 85, 247, 0.5);

  /* === INTERACTIVE STATES === */
  --hover-primary: #8B5CF6;
  --focus-ring: rgba(168, 85, 247, 0.6);

  /* === SIZING & SPACING === */
  --border-radius: 16px;
  --border-radius-sm: 8px;
  
  /* === ANIMATIONS === */
  --transition-normal: 0.3s ease;
  --transition-fast: 0.15s ease-out;
}

/* ==========================================================================
   #BASE & GLOBAL STYLES
   ========================================================================== */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Montserrat", sans-serif;
  background: var(--dark-base); /* Fallback color */
  background-image: var(--gradient-main-bg);
  background-attachment: fixed;
  color: var(--text-secondary); /* Softer default text color */
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: var(--dark-base);
}
::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: var(--secondary);
}

/* Custom Text Selection */
::selection {
  background: rgba(168, 85, 247, 0.3);
  color: var(--text-primary);
}
::-moz-selection {
  background: rgba(168, 85, 247, 0.3);
  color: var(--text-primary);
}


/* ==========================================================================
   #TYPOGRAPHY
   ========================================================================== */
.font-display {
    font-family: 'Neonderthaw', cursive;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  line-height: 1.2;
  font-weight: 600;
  margin-bottom: 1rem;
}

h1 {
  /* Reserved for the most important heading */
  font-size: clamp(3rem, 10vw, 5.5rem);
  font-weight: normal;
  text-shadow: var(--outline-glow);
}

h2 {
  font-size: clamp(2rem, 7vw, 3rem);
  font-weight: 700;
}

h3 {
  font-size: clamp(1.5rem, 5vw, 2rem);
  font-weight: 600;
}

p {
  margin-bottom: 1.5rem;
  max-width: 75ch; /* Improves readability */
}

a {
  color: var(--accent);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  text-decoration: underline;
  color: var(--hover-primary);
}

.text-gradient {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* ==========================================================================
   #LAYOUT & REUSABLE COMPONENTS
   ========================================================================== */

/* A simple container to center content */
.container {
  width: 90%;
  max-width: 1100px;
  margin-left: auto;
  margin-right: auto;
  padding: 4rem 0;
}

/* Main Glassmorphism Card */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  box-shadow: var(--glass-shadow);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal), border-color var(--transition-normal);
  padding: 2rem;
}

.glass-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 48px rgba(13, 6, 21, 0.9), var(--outline-glow);
  border-color: var(--glass-border-strong);
}

/* Section Divider */
.section-divider {
  height: 2px;
  width: 80%;
  max-width: 400px;
  background: linear-gradient(90deg, transparent, var(--accent), transparent);
  border: none;
  margin: 3rem auto;
  border-radius: 1px;
}

/* ==========================================================================
   #BUTTONS
   ========================================================================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: var(--border-radius-sm);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 1px solid transparent;
}
.btn:focus-visible {
  outline: 2px solid var(--focus-ring);
  outline-offset: 2px;
}
.btn:active {
  transform: translateY(1px);
  transition: transform var(--transition-fast);
}

/* Primary Button (Glassy) */
.btn-primary {
  background: var(--glass-bg);
  border-color: var(--glass-border);
  color: var(--text-primary);
}
.btn-primary:hover {
  background: var(--hover-primary);
  border-color: var(--hover-primary);
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.3);
  transform: translateY(-3px);
}

/* Accent Button (Main Call-to-Action) */
.btn-accent {
  background: var(--gradient-accent-btn);
  color: var(--text-primary);
  box-shadow: 0 4px 16px rgba(124, 45, 146, 0.4);
}
.btn-accent:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(124, 45, 146, 0.6);
}

/* Ghost Button */
.btn-ghost {
  background: transparent;
  border: 2px solid var(--glass-border);
  color: var(--text-primary);
  padding: 10px 22px; /* Adjust padding due to border size */
}
.btn-ghost:hover {
  background: var(--glass-bg);
  border-color: var(--accent);
}

/* ==========================================================================
   #STRATEGIC ANIMATIONS
   Use these sparingly on high-impact elements like a hero CTA.
   ========================================================================== */
.hero-image {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%   { transform: translateY(0px); }
  50%  { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

.call-to-action-btn {
  animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(168, 85, 247, 0.8);
  }
}

.navbar{
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  box-shadow: var(--glass-shadow);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal), border-color var(--transition-normal);
  padding: 2rem;
  position: fixed;
  top: 0;
  left: 0;
  width: max-content;
  z-index: 1000;
}
